<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EventHive - Project Presentation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .slide {
            min-height: 100vh;
            display: none;
            padding: 2rem;
            background: white;
            margin: 1rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .slide.active {
            display: block;
        }
        .slide-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 3px solid #667eea;
        }
        .slide-title {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        .slide-subtitle {
            font-size: 1.2rem;
            color: #666;
        }
        .tech-icon {
            font-size: 4rem;
            margin: 1rem;
            transition: transform 0.3s ease;
        }
        .tech-icon:hover {
            transform: scale(1.1);
        }
        .feature-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
            border-left: 5px solid #667eea;
        }
        .navigation {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
        .nav-btn {
            margin: 0 5px;
            padding: 10px 15px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .nav-btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        .slide-counter {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(102, 126, 234, 0.9);
            color: white;
            padding: 10px 15px;
            border-radius: 25px;
            font-weight: bold;
        }
        .cameroon-flag {
            background: linear-gradient(to right, #009639 33%, #ce1126 33%, #ce1126 66%, #fcdd09 66%);
            height: 20px;
            width: 60px;
            display: inline-block;
            border-radius: 3px;
            margin: 0 10px;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            margin: 1rem;
        }
        .stats-number {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        .code-snippet {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="slide-counter">
        <span id="currentSlide">1</span> / <span id="totalSlides">13</span>
    </div>

    <!-- Slide 1: Title -->
    <div class="slide active">
        <div class="slide-header">
            <div class="slide-title">
                <i class="fas fa-calendar-alt text-primary"></i> EventHive
            </div>
            <div class="slide-subtitle">Cameroon's Premier Event Booking Platform</div>
            <div class="cameroon-flag"></div>
        </div>
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h3 class="mb-4">Connecting Cameroon to Amazing Events</h3>
                <div class="feature-card">
                    <h5><i class="fas fa-rocket text-primary me-2"></i>Project Overview</h5>
                    <p>A comprehensive web-based event booking platform designed specifically for the Cameroon market, featuring secure ticket booking, QR code generation, and CFA franc pricing.</p>
                </div>
                <div class="feature-card">
                    <h5><i class="fas fa-users text-success me-2"></i>Target Audience</h5>
                    <p>Event enthusiasts, organizers, and businesses across Cameroon looking for a reliable platform to discover and book event tickets.</p>
                </div>
            </div>
            <div class="col-lg-6 text-center">
                <div class="stats-card">
                    <div class="stats-number">1000+</div>
                    <div>Events Supported</div>
                </div>
                <div class="stats-card">
                    <div class="stats-number">5000+</div>
                    <div>Users Served</div>
                </div>
                <div class="stats-card">
                    <div class="stats-number">10+</div>
                    <div>Cities Covered</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 2: Problem Statement -->
    <div class="slide">
        <div class="slide-header">
            <div class="slide-title">Problem Statement</div>
            <div class="slide-subtitle">Challenges in Cameroon's Event Industry</div>
        </div>
        <div class="row">
            <div class="col-lg-6">
                <h4 class="text-danger mb-4"><i class="fas fa-exclamation-triangle me-2"></i>Current Challenges</h4>
                <div class="feature-card">
                    <h6><i class="fas fa-search text-danger me-2"></i>Limited Event Discovery</h6>
                    <p>Difficulty finding events happening across different cities in Cameroon</p>
                </div>
                <div class="feature-card">
                    <h6><i class="fas fa-credit-card text-danger me-2"></i>Payment Complications</h6>
                    <p>Complex payment processes not optimized for local payment methods</p>
                </div>
                <div class="feature-card">
                    <h6><i class="fas fa-ticket-alt text-danger me-2"></i>Manual Ticketing</h6>
                    <p>Paper-based ticketing systems prone to fraud and inefficiency</p>
                </div>
                <div class="feature-card">
                    <h6><i class="fas fa-mobile-alt text-danger me-2"></i>Poor Mobile Experience</h6>
                    <p>Lack of mobile-optimized platforms for event booking</p>
                </div>
            </div>
            <div class="col-lg-6">
                <h4 class="text-primary mb-4"><i class="fas fa-lightbulb me-2"></i>Market Opportunity</h4>
                <div class="alert alert-info">
                    <h6><i class="fas fa-chart-line me-2"></i>Growing Digital Adoption</h6>
                    <p>Increasing smartphone penetration and internet usage in Cameroon creates opportunity for digital solutions.</p>
                </div>
                <div class="alert alert-success">
                    <h6><i class="fas fa-calendar me-2"></i>Event Industry Growth</h6>
                    <p>Rising number of conferences, festivals, and cultural events across major cities.</p>
                </div>
                <div class="alert alert-warning">
                    <h6><i class="fas fa-coins me-2"></i>Local Currency Need</h6>
                    <p>Requirement for CFA franc pricing and local payment integration.</p>
                </div>
                <div class="text-center mt-4">
                    <div class="bg-primary text-white p-4 rounded">
                        <h5>Solution Needed</h5>
                        <p class="mb-0">A localized, secure, and user-friendly event booking platform</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 3: Objectives & Goals -->
    <div class="slide">
        <div class="slide-header">
            <div class="slide-title">Objectives & Goals</div>
            <div class="slide-subtitle">What EventHive Aims to Achieve</div>
        </div>
        <div class="row">
            <div class="col-lg-6">
                <h4 class="text-primary mb-4"><i class="fas fa-bullseye me-2"></i>Primary Objectives</h4>
                <div class="feature-card">
                    <h6><i class="fas fa-globe-africa text-primary me-2"></i>Localization</h6>
                    <p>Create a platform specifically designed for the Cameroon market with CFA pricing and local city focus</p>
                </div>
                <div class="feature-card">
                    <h6><i class="fas fa-shield-alt text-success me-2"></i>Security</h6>
                    <p>Implement robust security measures for user data protection and secure payment processing</p>
                </div>
                <div class="feature-card">
                    <h6><i class="fas fa-mobile-alt text-info me-2"></i>Accessibility</h6>
                    <p>Ensure mobile-responsive design that works across all devices and connection speeds</p>
                </div>
                <div class="feature-card">
                    <h6><i class="fas fa-qrcode text-warning me-2"></i>Innovation</h6>
                    <p>Introduce QR code ticketing system for modern, fraud-resistant event entry</p>
                </div>
            </div>
            <div class="col-lg-6">
                <h4 class="text-success mb-4"><i class="fas fa-trophy me-2"></i>Success Metrics</h4>
                <div class="row">
                    <div class="col-6">
                        <div class="stats-card bg-success">
                            <div class="stats-number">95%</div>
                            <div>User Satisfaction</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stats-card bg-info">
                            <div class="stats-number">24/7</div>
                            <div>Platform Availability</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stats-card bg-warning">
                            <div class="stats-number">&lt;3s</div>
                            <div>Page Load Time</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stats-card bg-danger">
                            <div class="stats-number">99.9%</div>
                            <div>Security Uptime</div>
                        </div>
                    </div>
                </div>
                <div class="alert alert-primary mt-3">
                    <h6><i class="fas fa-target me-2"></i>Long-term Vision</h6>
                    <p class="mb-0">Become the leading event booking platform in Central Africa, expanding to neighboring countries while maintaining local focus.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 4: Development Stack -->
    <div class="slide">
        <div class="slide-header">
            <div class="slide-title">Development Stack</div>
            <div class="slide-subtitle">Technologies Powering EventHive</div>
        </div>
        <div class="row">
            <div class="col-lg-4">
                <h4 class="text-primary mb-4"><i class="fas fa-code me-2"></i>Frontend Technologies</h4>
                <div class="text-center">
                    <div class="tech-icon text-warning"><i class="fab fa-html5"></i></div>
                    <h6>HTML5</h6>
                    <p>Semantic markup and modern web standards</p>
                </div>
                <div class="text-center">
                    <div class="tech-icon text-primary"><i class="fab fa-css3-alt"></i></div>
                    <h6>CSS3</h6>
                    <p>Advanced styling and responsive design</p>
                </div>
                <div class="text-center">
                    <div class="tech-icon text-warning"><i class="fab fa-js-square"></i></div>
                    <h6>JavaScript</h6>
                    <p>Interactive user interface and AJAX functionality</p>
                </div>
                <div class="text-center">
                    <div class="tech-icon text-purple"><i class="fab fa-bootstrap"></i></div>
                    <h6>Bootstrap 5</h6>
                    <p>Responsive framework and UI components</p>
                </div>
            </div>
            <div class="col-lg-4">
                <h4 class="text-success mb-4"><i class="fas fa-server me-2"></i>Backend Technologies</h4>
                <div class="text-center">
                    <div class="tech-icon text-primary"><i class="fab fa-php"></i></div>
                    <h6>PHP 7.4+</h6>
                    <p>Server-side scripting and business logic</p>
                </div>
                <div class="text-center">
                    <div class="tech-icon text-info"><i class="fas fa-database"></i></div>
                    <h6>MySQL 5.7+</h6>
                    <p>Relational database management</p>
                </div>
                <div class="text-center">
                    <div class="tech-icon text-success"><i class="fas fa-shield-alt"></i></div>
                    <h6>PDO</h6>
                    <p>Secure database abstraction layer</p>
                </div>
                <div class="text-center">
                    <div class="tech-icon text-warning"><i class="fas fa-lock"></i></div>
                    <h6>Bcrypt</h6>
                    <p>Password hashing and security</p>
                </div>
            </div>
            <div class="col-lg-4">
                <h4 class="text-info mb-4"><i class="fas fa-tools me-2"></i>Additional Tools</h4>
                <div class="text-center">
                    <div class="tech-icon text-danger"><i class="fab fa-font-awesome"></i></div>
                    <h6>Font Awesome</h6>
                    <p>Icon library and visual elements</p>
                </div>
                <div class="text-center">
                    <div class="tech-icon text-success"><i class="fas fa-qrcode"></i></div>
                    <h6>QR Code Library</h6>
                    <p>Digital ticket generation</p>
                </div>
                <div class="text-center">
                    <div class="tech-icon text-primary"><i class="fab fa-git-alt"></i></div>
                    <h6>Version Control</h6>
                    <p>Code management and collaboration</p>
                </div>
                <div class="text-center">
                    <div class="tech-icon text-warning"><i class="fas fa-envelope"></i></div>
                    <h6>PHP Mail</h6>
                    <p>Email notifications and communication</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 5: System Architecture -->
    <div class="slide">
        <div class="slide-header">
            <div class="slide-title">System Architecture</div>
            <div class="slide-subtitle">EventHive Platform Structure</div>
        </div>
        <div class="row">
            <div class="col-lg-8">
                <h4 class="text-primary mb-4"><i class="fas fa-sitemap me-2"></i>Application Architecture</h4>
                <div class="code-snippet">
EventHive/
├── 🎯 Frontend Layer (HTML5, CSS3, Bootstrap, JavaScript)
├── 🔧 Application Layer (PHP 7.4+)
├── 💾 Data Layer (MySQL 5.7+)
├── 🔐 Security Layer (Authentication, Authorization)
└── 📱 API Layer (AJAX endpoints)
                </div>
                <div class="feature-card">
                    <h6><i class="fas fa-layer-group text-primary me-2"></i>Three-Tier Architecture</h6>
                    <p><strong>Presentation Tier:</strong> User interface and user experience<br>
                    <strong>Logic Tier:</strong> Business logic and data processing<br>
                    <strong>Data Tier:</strong> Database management and storage</p>
                </div>
            </div>
            <div class="col-lg-4">
                <h5 class="text-success mb-3"><i class="fas fa-database me-2"></i>Database Design</h5>
                <div class="alert alert-light">
                    <small>
                        <strong>Core Tables:</strong><br>
                        • users (authentication)<br>
                        • events (event data)<br>
                        • bookings (transactions)<br>
                        • admins (administration)<br>
                        • cart_items (shopping cart)
                    </small>
                </div>
                <h5 class="text-info mb-3"><i class="fas fa-shield-alt me-2"></i>Security Features</h5>
                <div class="alert alert-light">
                    <small>
                        • Password hashing (Bcrypt)<br>
                        • SQL injection prevention<br>
                        • XSS protection<br>
                        • Session management<br>
                        • Input validation
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 6: Key Features -->
    <div class="slide">
        <div class="slide-header">
            <div class="slide-title">Key Features</div>
            <div class="slide-subtitle">What Makes EventHive Special</div>
        </div>
        <div class="row">
            <div class="col-lg-6">
                <h4 class="text-primary mb-4"><i class="fas fa-star me-2"></i>User Features</h4>
                <div class="feature-card">
                    <h6><i class="fas fa-search text-primary me-2"></i>Smart Event Discovery</h6>
                    <p>Advanced search and filtering by location, date, and keywords</p>
                </div>
                <div class="feature-card">
                    <h6><i class="fas fa-shopping-cart text-success me-2"></i>Shopping Cart System</h6>
                    <p>AJAX-powered cart with real-time updates and quantity management</p>
                </div>
                <div class="feature-card">
                    <h6><i class="fas fa-qrcode text-warning me-2"></i>QR Code Tickets</h6>
                    <p>Digital tickets with QR codes for secure, contactless entry</p>
                </div>
                <div class="feature-card">
                    <h6><i class="fas fa-user-circle text-info me-2"></i>User Dashboard</h6>
                    <p>Complete booking history, profile management, and statistics</p>
                </div>
            </div>
            <div class="col-lg-6">
                <h4 class="text-success mb-4"><i class="fas fa-cogs me-2"></i>Admin Features</h4>
                <div class="feature-card">
                    <h6><i class="fas fa-chart-line text-primary me-2"></i>Analytics Dashboard</h6>
                    <p>Real-time statistics, booking trends, and revenue tracking</p>
                </div>
                <div class="feature-card">
                    <h6><i class="fas fa-calendar-plus text-success me-2"></i>Event Management</h6>
                    <p>Create, edit, and manage events with image uploads</p>
                </div>
                <div class="feature-card">
                    <h6><i class="fas fa-users text-warning me-2"></i>User Management</h6>
                    <p>Monitor user accounts, activity, and support requests</p>
                </div>
                <div class="feature-card">
                    <h6><i class="fas fa-file-export text-info me-2"></i>Data Export</h6>
                    <p>Generate reports in CSV and PDF formats for analysis</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 7: Development Process -->
    <div class="slide">
        <div class="slide-header">
            <div class="slide-title">Development Process</div>
            <div class="slide-subtitle">From Concept to Deployment</div>
        </div>
        <div class="row">
            <div class="col-lg-12">
                <div class="row">
                    <div class="col-md-3">
                        <div class="stats-card bg-primary">
                            <div class="stats-number"><i class="fas fa-lightbulb"></i></div>
                            <h6>Planning</h6>
                            <p>Requirements analysis, system design, and architecture planning</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card bg-success">
                            <div class="stats-number"><i class="fas fa-code"></i></div>
                            <h6>Development</h6>
                            <p>Frontend and backend implementation with security best practices</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card bg-warning">
                            <div class="stats-number"><i class="fas fa-bug"></i></div>
                            <h6>Testing</h6>
                            <p>Comprehensive testing including security, functionality, and performance</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card bg-info">
                            <div class="stats-number"><i class="fas fa-rocket"></i></div>
                            <h6>Deployment</h6>
                            <p>Production deployment with monitoring and maintenance setup</p>
                        </div>
                    </div>
                </div>
                <div class="feature-card mt-4">
                    <h5><i class="fas fa-tasks text-primary me-2"></i>Development Methodology</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Agile Approach</h6>
                            <ul>
                                <li>Iterative development cycles</li>
                                <li>Continuous integration and testing</li>
                                <li>Regular code reviews and refactoring</li>
                                <li>User feedback incorporation</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Quality Assurance</h6>
                            <ul>
                                <li>Code standards and documentation</li>
                                <li>Security vulnerability testing</li>
                                <li>Performance optimization</li>
                                <li>Cross-browser compatibility</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 8: Cameroon Localization -->
    <div class="slide">
        <div class="slide-header">
            <div class="slide-title">Cameroon Localization</div>
            <div class="slide-subtitle">Built for the Local Market <span class="cameroon-flag"></span></div>
        </div>
        <div class="row">
            <div class="col-lg-6">
                <h4 class="text-primary mb-4"><i class="fas fa-globe-africa me-2"></i>Local Features</h4>
                <div class="feature-card">
                    <h6><i class="fas fa-coins text-warning me-2"></i>CFA Franc Pricing</h6>
                    <p>All prices displayed in Central African CFA francs (XAF) for local users</p>
                </div>
                <div class="feature-card">
                    <h6><i class="fas fa-map-marker-alt text-success me-2"></i>Major Cities Focus</h6>
                    <p>Events categorized by major Cameroon cities: Yaoundé, Douala, Bafoussam, Garoua</p>
                </div>
                <div class="feature-card">
                    <h6><i class="fas fa-language text-info me-2"></i>Cultural Context</h6>
                    <p>Content and imagery reflecting Cameroon culture and local preferences</p>
                </div>
                <div class="feature-card">
                    <h6><i class="fas fa-phone text-primary me-2"></i>Local Support</h6>
                    <p>Customer support with local phone numbers and email addresses</p>
                </div>
            </div>
            <div class="col-lg-6">
                <h4 class="text-success mb-4"><i class="fas fa-calendar me-2"></i>Event Categories</h4>
                <div class="row">
                    <div class="col-6 mb-3">
                        <div class="alert alert-primary text-center">
                            <i class="fas fa-laptop fa-2x mb-2"></i>
                            <h6>Tech Conferences</h6>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="alert alert-success text-center">
                            <i class="fas fa-music fa-2x mb-2"></i>
                            <h6>Music Festivals</h6>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="alert alert-warning text-center">
                            <i class="fas fa-briefcase fa-2x mb-2"></i>
                            <h6>Business Events</h6>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="alert alert-info text-center">
                            <i class="fas fa-palette fa-2x mb-2"></i>
                            <h6>Art Exhibitions</h6>
                        </div>
                    </div>
                </div>
                <div class="bg-light p-3 rounded">
                    <h6><i class="fas fa-heart text-danger me-2"></i>Cultural Celebrations</h6>
                    <p class="mb-0">Supporting traditional festivals, cultural events, and national celebrations across Cameroon</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 9: Security & Privacy -->
    <div class="slide">
        <div class="slide-header">
            <div class="slide-title">Security & Privacy</div>
            <div class="slide-subtitle">Protecting User Data and Transactions</div>
        </div>
        <div class="row">
            <div class="col-lg-6">
                <h4 class="text-danger mb-4"><i class="fas fa-shield-alt me-2"></i>Security Measures</h4>
                <div class="feature-card">
                    <h6><i class="fas fa-lock text-danger me-2"></i>Password Security</h6>
                    <p>Bcrypt hashing with salt for secure password storage</p>
                </div>
                <div class="feature-card">
                    <h6><i class="fas fa-database text-warning me-2"></i>SQL Injection Prevention</h6>
                    <p>Prepared statements and parameterized queries</p>
                </div>
                <div class="feature-card">
                    <h6><i class="fas fa-code text-info me-2"></i>XSS Protection</h6>
                    <p>Input sanitization and output encoding</p>
                </div>
                <div class="feature-card">
                    <h6><i class="fas fa-user-shield text-success me-2"></i>Session Security</h6>
                    <p>Secure session handling and timeout management</p>
                </div>
            </div>
            <div class="col-lg-6">
                <h4 class="text-primary mb-4"><i class="fas fa-user-secret me-2"></i>Privacy Compliance</h4>
                <div class="alert alert-primary">
                    <h6><i class="fas fa-file-contract me-2"></i>Legal Documentation</h6>
                    <p>Comprehensive Terms of Service and Privacy Policy tailored for Cameroon law</p>
                </div>
                <div class="alert alert-success">
                    <h6><i class="fas fa-user-cog me-2"></i>User Rights</h6>
                    <p>Profile management, data access, and account deletion options</p>
                </div>
                <div class="alert alert-warning">
                    <h6><i class="fas fa-minimize me-2"></i>Data Minimization</h6>
                    <p>Collect only necessary information for service functionality</p>
                </div>
                <div class="alert alert-info">
                    <h6><i class="fas fa-envelope-open-text me-2"></i>Transparent Communication</h6>
                    <p>Clear privacy notices and consent mechanisms</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 10: Performance & Scalability -->
    <div class="slide">
        <div class="slide-header">
            <div class="slide-title">Performance & Scalability</div>
            <div class="slide-subtitle">Built for Growth and Speed</div>
        </div>
        <div class="row">
            <div class="col-lg-8">
                <h4 class="text-primary mb-4"><i class="fas fa-tachometer-alt me-2"></i>Performance Optimization</h4>
                <div class="row">
                    <div class="col-md-6">
                        <div class="feature-card">
                            <h6><i class="fas fa-compress text-primary me-2"></i>Code Optimization</h6>
                            <p>Minified CSS/JS, optimized database queries, and efficient algorithms</p>
                        </div>
                        <div class="feature-card">
                            <h6><i class="fas fa-images text-success me-2"></i>Image Optimization</h6>
                            <p>Compressed images, lazy loading, and responsive image delivery</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="feature-card">
                            <h6><i class="fas fa-memory text-warning me-2"></i>Caching Strategy</h6>
                            <p>Browser caching, database query optimization, and session management</p>
                        </div>
                        <div class="feature-card">
                            <h6><i class="fas fa-mobile-alt text-info me-2"></i>Mobile Optimization</h6>
                            <p>Responsive design, touch-friendly interface, and fast mobile loading</p>
                        </div>
                    </div>
                </div>
                <h4 class="text-success mb-4 mt-4"><i class="fas fa-expand-arrows-alt me-2"></i>Scalability Features</h4>
                <div class="code-snippet">
• Modular architecture for easy feature additions
• Database indexing for improved query performance
• AJAX implementation for reduced server load
• Efficient session management for concurrent users
• Optimized file structure for maintainability
                </div>
            </div>
            <div class="col-lg-4">
                <h5 class="text-info mb-3"><i class="fas fa-chart-bar me-2"></i>Performance Metrics</h5>
                <div class="stats-card bg-primary">
                    <div class="stats-number">&lt;2s</div>
                    <div>Average Load Time</div>
                </div>
                <div class="stats-card bg-success">
                    <div class="stats-number">99.9%</div>
                    <div>Uptime Target</div>
                </div>
                <div class="stats-card bg-warning">
                    <div class="stats-number">1000+</div>
                    <div>Concurrent Users</div>
                </div>
                <div class="stats-card bg-info">
                    <div class="stats-number">95+</div>
                    <div>PageSpeed Score</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 11: Conclusion -->
    <div class="slide">
        <div class="slide-header">
            <div class="slide-title">Conclusion</div>
            <div class="slide-subtitle">EventHive Impact and Success</div>
        </div>
        <div class="row">
            <div class="col-lg-6">
                <h4 class="text-success mb-4"><i class="fas fa-check-circle me-2"></i>Project Achievements</h4>
                <div class="feature-card">
                    <h6><i class="fas fa-target text-success me-2"></i>Objectives Met</h6>
                    <p>Successfully created a localized, secure, and user-friendly event booking platform for Cameroon</p>
                </div>
                <div class="feature-card">
                    <h6><i class="fas fa-users text-primary me-2"></i>User Experience</h6>
                    <p>Intuitive interface with mobile-responsive design and comprehensive functionality</p>
                </div>
                <div class="feature-card">
                    <h6><i class="fas fa-shield-alt text-warning me-2"></i>Security Implementation</h6>
                    <p>Robust security measures protecting user data and financial transactions</p>
                </div>
                <div class="feature-card">
                    <h6><i class="fas fa-globe-africa text-info me-2"></i>Local Market Focus</h6>
                    <p>Tailored specifically for Cameroon with CFA pricing and cultural relevance</p>
                </div>
            </div>
            <div class="col-lg-6">
                <h4 class="text-primary mb-4"><i class="fas fa-trophy me-2"></i>Key Success Factors</h4>
                <div class="alert alert-success">
                    <h6><i class="fas fa-code me-2"></i>Technical Excellence</h6>
                    <p>Modern web technologies with best practices in security and performance</p>
                </div>
                <div class="alert alert-primary">
                    <h6><i class="fas fa-heart me-2"></i>User-Centric Design</h6>
                    <p>Focus on user experience and accessibility across all devices</p>
                </div>
                <div class="alert alert-warning">
                    <h6><i class="fas fa-map-marker-alt me-2"></i>Local Relevance</h6>
                    <p>Deep understanding of Cameroon market needs and preferences</p>
                </div>
                <div class="alert alert-info">
                    <h6><i class="fas fa-rocket me-2"></i>Scalable Foundation</h6>
                    <p>Architecture designed for growth and future enhancements</p>
                </div>
                <div class="text-center mt-4">
                    <div class="bg-success text-white p-3 rounded">
                        <h5><i class="fas fa-star me-2"></i>Mission Accomplished</h5>
                        <p class="mb-0">EventHive successfully addresses the event booking challenges in Cameroon</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 12: Future Improvements -->
    <div class="slide">
        <div class="slide-header">
            <div class="slide-title">Future Improvements</div>
            <div class="slide-subtitle">Roadmap for EventHive Evolution</div>
        </div>
        <div class="row">
            <div class="col-lg-6">
                <h4 class="text-primary mb-4"><i class="fas fa-rocket me-2"></i>Short-term Enhancements</h4>
                <div class="feature-card">
                    <h6><i class="fas fa-credit-card text-primary me-2"></i>Payment Integration</h6>
                    <p>Integration with local payment gateways (Mobile Money, Orange Money, MTN MoMo)</p>
                </div>
                <div class="feature-card">
                    <h6><i class="fas fa-envelope text-success me-2"></i>Email Notifications</h6>
                    <p>Automated email confirmations, reminders, and event updates</p>
                </div>
                <div class="feature-card">
                    <h6><i class="fas fa-mobile-alt text-warning me-2"></i>Mobile App</h6>
                    <p>Native iOS and Android applications for enhanced mobile experience</p>
                </div>
                <div class="feature-card">
                    <h6><i class="fas fa-language text-info me-2"></i>Multi-language Support</h6>
                    <p>French language support for broader Cameroon market reach</p>
                </div>
            </div>
            <div class="col-lg-6">
                <h4 class="text-success mb-4"><i class="fas fa-globe me-2"></i>Long-term Vision</h4>
                <div class="feature-card">
                    <h6><i class="fas fa-map text-primary me-2"></i>Regional Expansion</h6>
                    <p>Expand to other Central African countries (Chad, CAR, Equatorial Guinea)</p>
                </div>
                <div class="feature-card">
                    <h6><i class="fas fa-robot text-success me-2"></i>AI Integration</h6>
                    <p>Machine learning for event recommendations and personalized experiences</p>
                </div>
                <div class="feature-card">
                    <h6><i class="fas fa-share-alt text-warning me-2"></i>Social Features</h6>
                    <p>Social media integration, event sharing, and community features</p>
                </div>
                <div class="feature-card">
                    <h6><i class="fas fa-chart-line text-info me-2"></i>Advanced Analytics</h6>
                    <p>Predictive analytics, market insights, and business intelligence tools</p>
                </div>
                <div class="text-center mt-4">
                    <div class="bg-primary text-white p-3 rounded">
                        <h5><i class="fas fa-flag me-2"></i>Ultimate Goal</h5>
                        <p class="mb-0">Become Central Africa's leading event platform</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 13: Thank You -->
    <div class="slide">
        <div class="slide-header">
            <div class="slide-title">Thank You</div>
            <div class="slide-subtitle">Questions & Discussion</div>
        </div>
        <div class="row align-items-center">
            <div class="col-lg-6 text-center">
                <div class="display-1 text-primary mb-4">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <h2 class="text-primary mb-3">EventHive</h2>
                <h4 class="text-muted mb-4">Connecting Cameroon to Amazing Events</h4>
                <div class="cameroon-flag mb-4"></div>
                <p class="lead">Built with ❤️ for the Cameroon event community</p>
            </div>
            <div class="col-lg-6">
                <div class="feature-card">
                    <h5><i class="fas fa-envelope text-primary me-2"></i>Contact Information</h5>
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Phone:</strong> +237 123 456 789</p>
                    <p><strong>Location:</strong> Yaoundé, Cameroon</p>
                </div>
                <div class="feature-card">
                    <h5><i class="fas fa-globe text-success me-2"></i>Project Links</h5>
                    <p><strong>Platform:</strong> EventHive Cameroon</p>
                    <p><strong>Demo:</strong> Available for testing</p>
                    <p><strong>Documentation:</strong> Complete user manual included</p>
                </div>
                <div class="text-center">
                    <h4 class="text-primary">Questions?</h4>
                    <p class="text-muted">Let's discuss EventHive and its potential impact on Cameroon's event industry</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()"><i class="fas fa-chevron-left"></i></button>
        <button class="nav-btn" onclick="nextSlide()"><i class="fas fa-chevron-right"></i></button>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentSlideIndex = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;
        
        document.getElementById('totalSlides').textContent = totalSlides;
        
        function showSlide(index) {
            slides.forEach(slide => slide.classList.remove('active'));
            slides[index].classList.add('active');
            document.getElementById('currentSlide').textContent = index + 1;
        }
        
        function nextSlide() {
            currentSlideIndex = (currentSlideIndex + 1) % totalSlides;
            showSlide(currentSlideIndex);
        }
        
        function previousSlide() {
            currentSlideIndex = (currentSlideIndex - 1 + totalSlides) % totalSlides;
            showSlide(currentSlideIndex);
        }
        
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowRight') nextSlide();
            if (e.key === 'ArrowLeft') previousSlide();
        });
    </script>
</body>
</html>
