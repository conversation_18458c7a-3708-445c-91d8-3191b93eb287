/* EventHive - Professional Event Management Platform */
/* Modern, Clean, Professional Design */

:root {
    /* EventHive Brand Colors */
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --primary-light: #8b5cf6;
    --secondary-color: #f59e0b;
    --secondary-dark: #d97706;
    --accent-color: #10b981;
    --accent-dark: #059669;
    
    /* Neutral Colors */
    --dark-color: #1f2937;
    --dark-light: #374151;
    --gray-color: #6b7280;
    --gray-light: #9ca3af;
    --light-color: #f9fafb;
    --white-color: #ffffff;
    
    /* Status Colors */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
    --gradient-dark: linear-gradient(135deg, var(--dark-color) 0%, var(--dark-light) 100%);
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    
    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
}

/* Global Styles */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--light-color);
    color: var(--dark-color);
    line-height: 1.6;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: var(--spacing-sm);
}

.display-1 { font-size: 3.5rem; font-weight: 700; }
.display-2 { font-size: 3rem; font-weight: 700; }
.display-3 { font-size: 2.5rem; font-weight: 600; }

/* EventHive Brand Elements */
.eventhive-logo {
    font-size: 1.75rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.eventhive-tagline {
    color: var(--gray-color);
    font-size: 0.875rem;
    font-weight: 500;
}

/* Navigation */
.navbar-eventhive {
    background: var(--white-color);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: var(--shadow-sm);
    padding: var(--spacing-sm) 0;
}

.navbar-eventhive .navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.navbar-eventhive .nav-link {
    color: var(--dark-color);
    font-weight: 500;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
}

.navbar-eventhive .nav-link:hover {
    background: var(--light-color);
    color: var(--primary-color);
}

.navbar-eventhive .nav-link.active {
    background: var(--gradient-primary);
    color: var(--white-color);
}

/* Buttons */
.btn-eventhive-primary {
    background: var(--gradient-primary);
    border: none;
    color: var(--white-color);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
}

.btn-eventhive-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: var(--white-color);
}

.btn-eventhive-secondary {
    background: var(--gradient-secondary);
    border: none;
    color: var(--white-color);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
}

.btn-eventhive-outline {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-lg);
    transition: all 0.3s ease;
}

.btn-eventhive-outline:hover {
    background: var(--primary-color);
    color: var(--white-color);
}

/* Cards */
.card-eventhive {
    background: var(--white-color);
    border: none;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all 0.3s ease;
}

.card-eventhive:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.card-eventhive-header {
    background: var(--gradient-primary);
    color: var(--white-color);
    padding: var(--spacing-md);
    border-bottom: none;
}

.card-eventhive-body {
    padding: var(--spacing-md);
}

/* Event Cards */
.event-card {
    background: var(--white-color);
    border: none;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    height: 100%;
}

.event-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.event-card-image {
    height: 200px;
    background: var(--gradient-primary);
    position: relative;
    overflow: hidden;
}

.event-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.event-card:hover .event-card-image img {
    transform: scale(1.05);
}

.event-card-badge {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    background: var(--gradient-secondary);
    color: var(--white-color);
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-lg);
    font-size: 0.75rem;
    font-weight: 600;
}

.event-card-body {
    padding: var(--spacing-md);
}

.event-card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: var(--spacing-xs);
}

.event-card-meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    font-size: 0.875rem;
    color: var(--gray-color);
}

.event-card-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

/* Hero Section */
.hero-eventhive {
    background: var(--gradient-primary);
    color: var(--white-color);
    padding: var(--spacing-xl) 0;
    position: relative;
    overflow: hidden;
}

.hero-eventhive::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
}

.hero-content {
    position: relative;
    z-index: 2;
}

/* Statistics Cards */
.stat-card {
    background: var(--white-color);
    border: none;
    border-radius: var(--radius-xl);
    padding: var(--spacing-md);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-sm);
    font-size: 1.5rem;
}

.stat-icon-primary { background: var(--gradient-primary); color: var(--white-color); }
.stat-icon-secondary { background: var(--gradient-secondary); color: var(--white-color); }
.stat-icon-success { background: linear-gradient(135deg, var(--success-color), var(--accent-color)); color: var(--white-color); }
.stat-icon-info { background: linear-gradient(135deg, var(--info-color), var(--primary-color)); color: var(--white-color); }

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.25rem;
}

.stat-label {
    color: var(--gray-color);
    font-size: 0.875rem;
    font-weight: 500;
}

/* Forms */
.form-eventhive {
    background: var(--white-color);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-lg);
}

.form-control-eventhive {
    border: 2px solid #e5e7eb;
    border-radius: var(--radius-lg);
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control-eventhive:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* Tables */
.table-eventhive {
    background: var(--white-color);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.table-eventhive thead {
    background: var(--gradient-primary);
    color: var(--white-color);
}

.table-eventhive th {
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    font-weight: 600;
}

.table-eventhive td {
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-bottom: 1px solid #f3f4f6;
}

/* Badges */
.badge-eventhive {
    padding: 0.375rem 0.75rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 0.75rem;
}

.badge-eventhive-primary { background: var(--gradient-primary); color: var(--white-color); }
.badge-eventhive-success { background: var(--success-color); color: var(--white-color); }
.badge-eventhive-warning { background: var(--warning-color); color: var(--white-color); }
.badge-eventhive-danger { background: var(--danger-color); color: var(--white-color); }

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-eventhive {
        padding: var(--spacing-lg) 0;
    }
    
    .display-1 { font-size: 2.5rem; }
    .display-2 { font-size: 2rem; }
    .display-3 { font-size: 1.75rem; }
    
    .event-card-image {
        height: 150px;
    }
    
    .stat-card {
        margin-bottom: var(--spacing-sm);
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #111827;
        --white-color: #1f2937;
        --dark-color: #f9fafb;
        --gray-color: #d1d5db;
    }
}
