<?php
require_once 'config/database.php';
$page_title = 'Home';

// Get search parameters
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$location = isset($_GET['location']) ? trim($_GET['location']) : '';
$date = isset($_GET['date']) ? $_GET['date'] : '';

// Build query
$query = "SELECT * FROM events WHERE status = 'active'";
$params = [];

if (!empty($search)) {
    $query .= " AND (name LIKE ? OR description LIKE ? OR organizer LIKE ?)";
    $searchTerm = "%$search%";
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

if (!empty($location)) {
    $query .= " AND location LIKE ?";
    $params[] = "%$location%";
}

if (!empty($date)) {
    $query .= " AND date = ?";
    $params[] = $date;
}

$query .= " ORDER BY date ASC, time ASC";

try {
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $events = $stmt->fetchAll();
} catch (PDOException $e) {
    $events = [];
}

include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero-eventhive">
    <div class="container">
        <div class="row align-items-center min-vh-75">
            <div class="col-lg-6 hero-content">
                <div class="animate-fade-in-up">
                    <h1 class="display-2 fw-bold mb-4">
                        Discover <span style="background: linear-gradient(135deg, #f59e0b, #d97706); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">Amazing</span> Events
                    </h1>
                    <p class="lead mb-4 opacity-90">
                        Join thousands discovering and booking the best events across Cameroon.
                        From tech conferences to music festivals, find your next unforgettable experience.
                    </p>
                    <div class="eventhive-tagline mb-4 text-white">
                        🇨🇲 Cameroon's Premier Event Platform
                    </div>
                    <div class="d-flex flex-wrap gap-3">
                        <a href="#events" class="btn btn-eventhive-secondary btn-lg">
                            <i class="fas fa-rocket me-2"></i>Explore Events
                        </a>
                        <a href="auth/register.php" class="btn btn-eventhive-outline btn-lg">
                            <i class="fas fa-star me-2"></i>Join EventHive
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="text-center position-relative">
                    <div class="position-relative d-inline-block">
                        <div class="stat-icon stat-icon-secondary" style="width: 200px; height: 200px; font-size: 4rem; margin: 0 auto;">
                            <i class="fas fa-calendar-star"></i>
                        </div>
                        <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center">
                            <div class="text-center text-white">
                                <div class="h2 fw-bold mb-0"><?php echo number_format(count($events)); ?>+</div>
                                <small>Events</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Search Section -->
<section class="py-5" style="background: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="form-eventhive">
                    <div class="text-center mb-4">
                        <h3 class="fw-bold text-dark">Find Your Perfect Event</h3>
                        <p class="text-muted">Search through hundreds of events across Cameroon</p>
                    </div>
                    <form method="GET" class="row g-4">
                        <div class="col-md-4">
                            <label for="search" class="form-label fw-semibold">
                                <i class="fas fa-search me-2 text-primary"></i>Search Events
                            </label>
                            <input type="text" class="form-control-eventhive" id="search" name="search"
                                   placeholder="Event name, organizer..." value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="location" class="form-label fw-semibold">
                                <i class="fas fa-map-marker-alt me-2 text-primary"></i>Location
                            </label>
                            <input type="text" class="form-control-eventhive" id="location" name="location"
                                   placeholder="Yaoundé, Douala..." value="<?php echo htmlspecialchars($location); ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="date" class="form-label fw-semibold">
                                <i class="fas fa-calendar me-2 text-primary"></i>Date
                            </label>
                            <input type="date" class="form-control-eventhive" id="date" name="date"
                                   value="<?php echo htmlspecialchars($date); ?>">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-eventhive-primary">
                                    <i class="fas fa-search me-2"></i>Search
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Events Section -->
<section id="events" class="py-5" id="featured-events">
    <div class="container">
        <div class="row mb-5">
            <div class="col">
                <div class="text-center">
                    <h2 class="display-3 fw-bold mb-3">
                        <?php if (!empty($search) || !empty($location) || !empty($date)): ?>
                            <i class="fas fa-search me-3 text-primary"></i>Search Results
                        <?php else: ?>
                            <i class="fas fa-star me-3 text-primary"></i>Featured Events
                        <?php endif; ?>
                    </h2>
                    <p class="lead text-muted mb-4">Discover amazing experiences happening across Cameroon</p>

                    <?php if (!empty($search) || !empty($location) || !empty($date)): ?>
                        <div class="text-center mb-4">
                            <a href="index.php" class="btn btn-eventhive-outline">
                                <i class="fas fa-times me-2"></i>Clear Filters
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <?php if (empty($events)): ?>
            <div class="text-center py-5">
                <div class="stat-icon stat-icon-primary mx-auto mb-4" style="width: 100px; height: 100px; font-size: 2.5rem;">
                    <i class="fas fa-calendar-times"></i>
                </div>
                <h4 class="fw-bold text-dark mb-3">No Events Found</h4>
                <p class="text-muted mb-4">Try adjusting your search criteria or check back later for new events.</p>
                <a href="index.php" class="btn btn-eventhive-primary">
                    <i class="fas fa-refresh me-2"></i>Browse All Events
                </a>
            </div>
        <?php else: ?>
            <div class="row g-4">
                <?php foreach ($events as $event): ?>
                    <div class="col-lg-4 col-md-6">
                        <div class="event-card">
                            <div class="event-card-image">
                                <?php if ($event['image']): ?>
                                    <img src="images/events/<?php echo htmlspecialchars($event['image']); ?>"
                                         alt="<?php echo htmlspecialchars($event['name']); ?>"
                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                    <div class="d-flex align-items-center justify-content-center h-100 w-100" style="display: none;">
                                        <i class="fas fa-calendar-star fa-3x text-white opacity-50"></i>
                                    </div>
                                <?php else: ?>
                                    <div class="d-flex align-items-center justify-content-center h-100 w-100">
                                        <i class="fas fa-calendar-star fa-3x text-white opacity-50"></i>
                                    </div>
                                <?php endif; ?>

                                <div class="event-card-badge">
                                    <?php echo number_format($event['price'], 0); ?> CFA
                                </div>
                            </div>

                            <div class="event-card-body">
                                <h5 class="event-card-title"><?php echo htmlspecialchars($event['name']); ?></h5>

                                <div class="event-card-meta">
                                    <span>
                                        <i class="fas fa-calendar text-primary"></i>
                                        <?php echo date('M d, Y', strtotime($event['date'])); ?>
                                    </span>
                                    <span>
                                        <i class="fas fa-clock text-primary"></i>
                                        <?php echo date('g:i A', strtotime($event['time'])); ?>
                                    </span>
                                </div>

                                <div class="event-card-meta">
                                    <span>
                                        <i class="fas fa-map-marker-alt text-primary"></i>
                                        <?php echo htmlspecialchars($event['location']); ?>
                                    </span>
                                </div>

                                <div class="event-card-meta mb-3">
                                    <span>
                                        <i class="fas fa-user text-primary"></i>
                                        <?php echo htmlspecialchars($event['organizer']); ?>
                                    </span>
                                </div>

                                <p class="text-muted mb-4" style="font-size: 0.9rem; line-height: 1.5;">
                                    <?php echo htmlspecialchars(substr($event['description'], 0, 120)) . '...'; ?>
                                </p>

                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div class="event-card-price"><?php echo number_format($event['price'], 0); ?> CFA</div>
                                    <small class="text-muted">
                                        <i class="fas fa-ticket-alt me-1"></i>
                                        <?php echo $event['available_tickets']; ?> left
                                    </small>
                                </div>

                                <div class="d-grid">
                                    <a href="event_details.php?id=<?php echo $event['id']; ?>" class="btn btn-eventhive-primary">
                                        <i class="fas fa-arrow-right me-2"></i>View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
