<?php
require_once 'config/database.php';
require_once 'includes/session.php';
require_once 'includes/qr_generator.php';

// Require login
requireLogin();

$user_id = getCurrentUserId();
$booking_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$format = isset($_GET['format']) ? $_GET['format'] : 'html'; // html or pdf

if (!$booking_id) {
    $_SESSION['error_message'] = 'Invalid booking ID.';
    header('Location: dashboard.php');
    exit();
}

try {
    // Get booking details with event information
    $stmt = $pdo->prepare("
        SELECT b.*, e.name, e.description, e.date, e.time, e.venue, e.location, e.organizer, e.organizer_contact
        FROM bookings b
        JOIN events e ON b.event_id = e.id
        WHERE b.id = ? AND b.user_id = ? AND b.booking_status = 'confirmed'
    ");
    $stmt->execute([$booking_id, $user_id]);
    $booking = $stmt->fetch();

    if (!$booking) {
        $_SESSION['error_message'] = 'Booking not found or access denied.';
        header('Location: dashboard.php');
        exit();
    }

    // Check if event is upcoming
    $event_date = strtotime($booking['date']);
    $current_date = strtotime('today');

    if ($event_date < $current_date) {
        $_SESSION['error_message'] = 'Tickets can only be downloaded for upcoming events.';
        header('Location: dashboard.php');
        exit();
    }

} catch (PDOException $e) {
    $_SESSION['error_message'] = 'Database error occurred.';
    header('Location: dashboard.php');
    exit();
}

// Generate ticket HTML
$ticketHTML = TicketGenerator::generateTicketHTML($booking, $booking);

// Set headers for download
$filename = 'ticket_' . $booking['booking_reference'] . '.html';

// Force download headers
header('Content-Type: application/octet-stream');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Content-Transfer-Encoding: binary');
header('Content-Length: ' . strlen($ticketHTML));
header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
header('Pragma: public');
header('Expires: 0');

// Clear any previous output
if (ob_get_level()) {
    ob_end_clean();
}

// Output the ticket
echo $ticketHTML;
exit();
?>
