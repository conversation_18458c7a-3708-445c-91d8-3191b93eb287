/* Custom CSS for Event Booking System */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

.main-content {
    min-height: calc(100vh - 200px);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
}

.hero-section .container {
    position: relative;
    z-index: 1;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 10px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
}

.card-img-top {
    border-radius: 10px 10px 0 0;
}

/* Button Styles */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(13, 110, 253, 0.3);
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Badge Styles */
.badge {
    font-size: 0.7rem;
    padding: 0.3em 0.5em;
}

/* Form Styles */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Alert Styles */
.alert {
    border-radius: 8px;
    border: none;
}

/* Footer */
footer {
    margin-top: auto;
}

footer a:hover {
    color: var(--primary-color) !important;
}

/* Event Card Specific */
.event-card {
    transition: all 0.3s ease;
}

.event-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Price Display */
.price-display {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--success-color);
}

/* Cart Styles */
.cart-item {
    border-bottom: 1px solid #e9ecef;
    padding: 1rem 0;
}

.cart-item:last-child {
    border-bottom: none;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-controls button {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Dashboard Styles */
.dashboard-card {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-left: 4px solid var(--primary-color);
}

.stat-card {
    text-align: center;
    padding: 2rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-color);
}

/* Booking Status */
.status-confirmed {
    color: var(--success-color);
}

.status-cancelled {
    color: var(--danger-color);
}

.status-pending {
    color: var(--warning-color);
}

/* QR Code */
.qr-code {
    max-width: 150px;
    height: auto;
}

/* Admin Panel */
.admin-sidebar {
    background: var(--dark-color);
    min-height: 100vh;
}

.admin-sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1rem;
    border-radius: 5px;
    margin-bottom: 0.25rem;
}

.admin-sidebar .nav-link:hover,
.admin-sidebar .nav-link.active {
    background: var(--primary-color);
    color: white;
}

/* Table Styles */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background: var(--light-color);
    border: none;
    font-weight: 600;
}

/* Loading Spinner */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
        padding: 3rem 0;
    }
    
    .hero-section .display-4 {
        font-size: 2rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-custom {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.border-radius-custom {
    border-radius: 15px;
}
