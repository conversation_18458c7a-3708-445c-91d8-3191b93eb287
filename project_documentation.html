<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EventHive - Project Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Times New Roman', serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }
        .document-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 3rem;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .document-header {
            text-align: center;
            border-bottom: 3px solid #667eea;
            padding-bottom: 2rem;
            margin-bottom: 3rem;
        }
        .document-title {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 1rem;
        }
        .document-subtitle {
            font-size: 1.5rem;
            color: #666;
            margin-bottom: 0.5rem;
        }
        .section-title {
            font-size: 1.8rem;
            font-weight: bold;
            color: #667eea;
            margin-top: 3rem;
            margin-bottom: 1.5rem;
            border-left: 5px solid #667eea;
            padding-left: 1rem;
        }
        .subsection-title {
            font-size: 1.4rem;
            font-weight: bold;
            color: #333;
            margin-top: 2rem;
            margin-bottom: 1rem;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin: 1rem 0;
            overflow-x: auto;
        }
        .feature-list {
            background: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 1rem;
            margin: 1rem 0;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .info-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .table-of-contents {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 2rem;
            margin: 2rem 0;
        }
        .toc-item {
            margin: 0.5rem 0;
            padding-left: 1rem;
        }
        .toc-item a {
            text-decoration: none;
            color: #667eea;
        }
        .toc-item a:hover {
            text-decoration: underline;
        }
        .cameroon-flag {
            background: linear-gradient(to right, #009639 33%, #ce1126 33%, #ce1126 66%, #fcdd09 66%);
            height: 20px;
            width: 60px;
            display: inline-block;
            border-radius: 3px;
            margin: 0 10px;
        }
        @media print {
            body { background: white; }
            .document-container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="document-container">
        <!-- Document Header -->
        <div class="document-header">
            <div class="document-title">
                <i class="fas fa-calendar-alt text-primary"></i> EventHive
            </div>
            <div class="document-subtitle">Project Documentation</div>
            <div class="text-muted">Cameroon's Premier Event Booking Platform</div>
            <div class="cameroon-flag mt-3"></div>
            <div class="mt-3">
                <strong>Version:</strong> 1.0 | 
                <strong>Date:</strong> <?php echo date('F d, Y'); ?> | 
                <strong>Author:</strong> EventHive Development Team
            </div>
        </div>

        <!-- Table of Contents -->
        <div class="table-of-contents">
            <h3 class="text-center mb-4"><i class="fas fa-list me-2"></i>Table of Contents</h3>
            <div class="row">
                <div class="col-md-6">
                    <div class="toc-item"><a href="#overview">1. Project Overview</a></div>
                    <div class="toc-item"><a href="#design">2. System Design</a></div>
                    <div class="toc-item"><a href="#implementation">3. Implementation</a></div>
                    <div class="toc-item"><a href="#deployment">4. Deployment</a></div>
                </div>
                <div class="col-md-6">
                    <div class="toc-item"><a href="#code-explanation">5. Code Explanation</a></div>
                    <div class="toc-item"><a href="#user-manual">6. User Manual</a></div>
                    <div class="toc-item"><a href="#appendices">7. Appendices</a></div>
                    <div class="toc-item"><a href="#references">8. References</a></div>
                </div>
            </div>
        </div>

        <!-- 1. Project Overview -->
        <div id="overview" class="section-title">1. Project Overview</div>
        
        <div class="subsection-title">1.1 Introduction</div>
        <p>EventHive is a comprehensive web-based event booking platform specifically designed for the Cameroon market. The platform serves as a bridge between event organizers and attendees, providing a secure, user-friendly, and culturally relevant solution for event discovery, booking, and management.</p>
        
        <div class="feature-list">
            <h6><i class="fas fa-star text-warning me-2"></i>Key Highlights</h6>
            <ul>
                <li>Localized for Cameroon with CFA franc pricing</li>
                <li>QR code-enabled digital ticketing system</li>
                <li>Comprehensive admin panel for event management</li>
                <li>Mobile-responsive design for all devices</li>
                <li>Robust security and privacy protection</li>
            </ul>
        </div>

        <div class="subsection-title">1.2 Problem Statement</div>
        <p>The event industry in Cameroon faces several challenges:</p>
        <ul>
            <li><strong>Limited Digital Presence:</strong> Most events rely on traditional marketing and manual ticketing</li>
            <li><strong>Payment Complications:</strong> Lack of integrated local payment solutions</li>
            <li><strong>Security Concerns:</strong> Paper tickets are prone to fraud and counterfeiting</li>
            <li><strong>Discovery Issues:</strong> Difficulty finding events across different cities</li>
            <li><strong>Mobile Accessibility:</strong> Limited mobile-optimized event platforms</li>
        </ul>

        <div class="subsection-title">1.3 Solution Approach</div>
        <p>EventHive addresses these challenges through:</p>
        <div class="feature-list">
            <ul>
                <li><strong>Digital Platform:</strong> Centralized online platform for event discovery and booking</li>
                <li><strong>Local Integration:</strong> CFA franc pricing and Cameroon city focus</li>
                <li><strong>Security Features:</strong> QR code tickets and secure user authentication</li>
                <li><strong>Mobile Optimization:</strong> Responsive design for smartphones and tablets</li>
                <li><strong>Comprehensive Management:</strong> Tools for both users and administrators</li>
            </ul>
        </div>

        <div class="subsection-title">1.4 Target Audience</div>
        <div class="row">
            <div class="col-md-4">
                <div class="info-box">
                    <h6><i class="fas fa-users text-primary me-2"></i>Event Attendees</h6>
                    <p>Individuals and groups looking to discover and book tickets for events across Cameroon</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="info-box">
                    <h6><i class="fas fa-bullhorn text-success me-2"></i>Event Organizers</h6>
                    <p>Organizations and individuals hosting events who need a platform to sell tickets and manage attendees</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="info-box">
                    <h6><i class="fas fa-cogs text-warning me-2"></i>Administrators</h6>
                    <p>Platform managers responsible for overseeing events, users, and system operations</p>
                </div>
            </div>
        </div>

        <div class="subsection-title">1.5 Project Scope</div>
        <p>The EventHive platform encompasses:</p>
        <ul>
            <li><strong>Frontend Development:</strong> User interface for event browsing, booking, and management</li>
            <li><strong>Backend Development:</strong> Server-side logic, database management, and API endpoints</li>
            <li><strong>Database Design:</strong> Structured data storage for users, events, and bookings</li>
            <li><strong>Security Implementation:</strong> Authentication, authorization, and data protection</li>
            <li><strong>Admin Panel:</strong> Comprehensive management interface for platform administration</li>
            <li><strong>Documentation:</strong> User manuals, technical documentation, and deployment guides</li>
        </ul>

        <!-- 2. System Design -->
        <div id="design" class="section-title">2. System Design</div>
        
        <div class="subsection-title">2.1 Architecture Overview</div>
        <p>EventHive follows a three-tier architecture pattern:</p>
        
        <div class="code-block">
┌─────────────────────────────────────────────────────────────┐
│                    PRESENTATION TIER                        │
│  HTML5, CSS3, Bootstrap 5, JavaScript, Font Awesome        │
├─────────────────────────────────────────────────────────────┤
│                    APPLICATION TIER                         │
│     PHP 7.4+, Session Management, Business Logic           │
├─────────────────────────────────────────────────────────────┤
│                      DATA TIER                              │
│        MySQL 5.7+, Database Schema, Data Storage           │
└─────────────────────────────────────────────────────────────┘
        </div>

        <div class="subsection-title">2.2 Database Design</div>
        <p>The database schema consists of the following core tables:</p>
        
        <div class="feature-list">
            <h6><i class="fas fa-database text-primary me-2"></i>Core Tables</h6>
            <ul>
                <li><strong>users:</strong> User account information and authentication data</li>
                <li><strong>events:</strong> Event details, pricing, and availability</li>
                <li><strong>bookings:</strong> Ticket purchases and transaction records</li>
                <li><strong>admins:</strong> Administrative user accounts</li>
                <li><strong>cart_items:</strong> Shopping cart functionality</li>
            </ul>
        </div>

        <div class="code-block">
-- Example table structure
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    phone VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('active', 'inactive', 'deleted') DEFAULT 'active'
);

CREATE TABLE events (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    date DATE NOT NULL,
    time TIME NOT NULL,
    location VARCHAR(200) NOT NULL,
    organizer VARCHAR(100) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    available_tickets INT NOT NULL,
    image VARCHAR(255),
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
        </div>

        <div class="subsection-title">2.3 Security Architecture</div>
        <p>Security is implemented at multiple layers:</p>
        
        <div class="warning-box">
            <h6><i class="fas fa-shield-alt text-danger me-2"></i>Security Measures</h6>
            <ul>
                <li><strong>Authentication:</strong> Secure login with password hashing (Bcrypt)</li>
                <li><strong>Authorization:</strong> Role-based access control for users and admins</li>
                <li><strong>Data Protection:</strong> SQL injection prevention with prepared statements</li>
                <li><strong>Input Validation:</strong> XSS protection through input sanitization</li>
                <li><strong>Session Security:</strong> Secure session management and timeout</li>
            </ul>
        </div>

        <div class="subsection-title">2.4 User Interface Design</div>
        <p>The UI design follows modern web standards with focus on:</p>
        <ul>
            <li><strong>Responsive Design:</strong> Mobile-first approach using Bootstrap 5</li>
            <li><strong>Accessibility:</strong> WCAG guidelines for inclusive design</li>
            <li><strong>User Experience:</strong> Intuitive navigation and clear information hierarchy</li>
            <li><strong>Performance:</strong> Optimized loading times and smooth interactions</li>
            <li><strong>Localization:</strong> Cameroon-specific content and cultural elements</li>
        </ul>

        <!-- 3. Implementation -->
        <div id="implementation" class="section-title">3. Implementation</div>
        
        <div class="subsection-title">3.1 Technology Stack</div>
        <div class="row">
            <div class="col-md-6">
                <div class="info-box">
                    <h6><i class="fas fa-code text-primary me-2"></i>Frontend Technologies</h6>
                    <ul>
                        <li>HTML5 - Semantic markup</li>
                        <li>CSS3 - Advanced styling</li>
                        <li>Bootstrap 5 - Responsive framework</li>
                        <li>JavaScript - Client-side interactivity</li>
                        <li>jQuery - DOM manipulation</li>
                        <li>Font Awesome - Icon library</li>
                    </ul>
                </div>
            </div>
            <div class="col-md-6">
                <div class="info-box">
                    <h6><i class="fas fa-server text-success me-2"></i>Backend Technologies</h6>
                    <ul>
                        <li>PHP 7.4+ - Server-side scripting</li>
                        <li>MySQL 5.7+ - Database management</li>
                        <li>PDO - Database abstraction</li>
                        <li>Sessions - User state management</li>
                        <li>QR Code Library - Ticket generation</li>
                        <li>PHP Mail - Email functionality</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="subsection-title">3.2 File Structure</div>
        <div class="code-block">
EventHive/
├── admin/                  # Administrative panel
│   ├── includes/          # Admin-specific includes
│   ├── index.php         # Admin dashboard
│   ├── events.php        # Event management
│   ├── users.php         # User management
│   ├── bookings.php      # Booking management
│   └── reports.php       # Analytics and reports
├── auth/                  # Authentication system
│   ├── login.php         # User login
│   ├── register.php      # User registration
│   └── logout.php        # Session termination
├── config/               # Configuration files
│   └── database.php      # Database connection
├── includes/             # Shared components
│   ├── header.php        # Site header
│   ├── footer.php        # Site footer
│   ├── session.php       # Session management
│   └── qr_generator.php  # QR code generation
├── images/               # Static assets
│   └── events/           # Event images
├── css/                  # Stylesheets
├── js/                   # JavaScript files
├── ajax/                 # AJAX endpoints
├── database/             # Database schema
├── index.php             # Homepage
├── events.php            # Event listings
├── event_details.php     # Event details
├── cart.php              # Shopping cart
├── checkout.php          # Checkout process
├── dashboard.php         # User dashboard
├── profile.php           # User profile
├── contact.php           # Contact form
├── about.php             # About page
├── terms.php             # Terms of service
├── privacy.php           # Privacy policy
└── README.md             # Project documentation
        </div>

        <div class="subsection-title">3.3 Core Features Implementation</div>

        <div class="feature-list">
            <h6><i class="fas fa-user-circle text-primary me-2"></i>User Authentication System</h6>
            <ul>
                <li>Secure registration with email validation</li>
                <li>Password hashing using PHP's password_hash() function</li>
                <li>Session-based authentication with timeout</li>
                <li>Profile management and account settings</li>
                <li>Password reset functionality</li>
            </ul>
        </div>

        <div class="feature-list">
            <h6><i class="fas fa-search text-success me-2"></i>Event Discovery & Search</h6>
            <ul>
                <li>Advanced search with multiple filters</li>
                <li>Location-based event filtering</li>
                <li>Date range selection</li>
                <li>Keyword search across event details</li>
                <li>Responsive event card layout</li>
            </ul>
        </div>

        <div class="feature-list">
            <h6><i class="fas fa-shopping-cart text-warning me-2"></i>Shopping Cart & Checkout</h6>
            <ul>
                <li>AJAX-powered cart functionality</li>
                <li>Real-time cart updates</li>
                <li>Quantity management</li>
                <li>Secure checkout process</li>
                <li>Order confirmation and receipt generation</li>
            </ul>
        </div>

        <div class="feature-list">
            <h6><i class="fas fa-qrcode text-info me-2"></i>Digital Ticketing System</h6>
            <ul>
                <li>QR code generation for each ticket</li>
                <li>Downloadable PDF tickets</li>
                <li>Printable receipt format</li>
                <li>Offline QR code scanning capability</li>
                <li>Fraud prevention through unique codes</li>
            </ul>
        </div>

        <!-- 4. Deployment -->
        <div id="deployment" class="section-title">4. Deployment</div>

        <div class="subsection-title">4.1 System Requirements</div>
        <div class="warning-box">
            <h6><i class="fas fa-server text-warning me-2"></i>Minimum Requirements</h6>
            <ul>
                <li><strong>Web Server:</strong> Apache 2.4+ or Nginx 1.18+</li>
                <li><strong>PHP:</strong> Version 7.4 or higher</li>
                <li><strong>Database:</strong> MySQL 5.7+ or MariaDB 10.3+</li>
                <li><strong>Memory:</strong> 512MB RAM minimum, 1GB recommended</li>
                <li><strong>Storage:</strong> 1GB disk space minimum</li>
                <li><strong>Extensions:</strong> PDO MySQL, GD, Mail, Session</li>
            </ul>
        </div>

        <div class="subsection-title">4.2 Installation Steps</div>
        <div class="code-block">
# 1. Download and extract EventHive files
# Place files in web server directory (e.g., /var/www/html/eventhive)

# 2. Create database
mysql -u root -p
CREATE DATABASE eventhive;
EXIT;

# 3. Import database schema
mysql -u username -p eventhive < database/schema.sql

# 4. Configure database connection
# Edit config/database.php with your credentials

# 5. Set file permissions
chmod 755 images/events/
chmod 644 config/database.php

# 6. Create admin account
# Visit: your-domain.com/admin/create_admin.php
# Follow the setup wizard
        </div>

        <div class="subsection-title">4.3 Configuration</div>
        <p>Key configuration files and settings:</p>

        <div class="code-block">
// config/database.php
&lt;?php
$host = 'localhost';
$dbname = 'eventhive';
$username = 'your_username';
$password = 'your_password';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8",
                   $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}
?&gt;
        </div>

        <div class="subsection-title">4.4 Security Considerations</div>
        <div class="warning-box">
            <h6><i class="fas fa-shield-alt text-danger me-2"></i>Production Security Checklist</h6>
            <ul>
                <li>Change default admin credentials immediately</li>
                <li>Use HTTPS for all communications</li>
                <li>Regular database backups</li>
                <li>Keep PHP and MySQL updated</li>
                <li>Implement firewall rules</li>
                <li>Monitor access logs</li>
                <li>Regular security audits</li>
            </ul>
        </div>

        <!-- 5. Code Explanation -->
        <div id="code-explanation" class="section-title">5. Code Explanation</div>

        <div class="subsection-title">5.1 Authentication System</div>
        <p>The authentication system provides secure user login and registration:</p>

        <div class="code-block">
// User Registration (auth/register.php)
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = trim($_POST['username']);
    $email = trim($_POST['email']);
    $password = $_POST['password'];

    // Validate input
    if (empty($username) || empty($email) || empty($password)) {
        $error = 'All fields are required.';
    } else {
        // Hash password
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);

        // Insert user
        $stmt = $pdo->prepare("INSERT INTO users (username, email, password) VALUES (?, ?, ?)");
        $stmt->execute([$username, $email, $hashed_password]);
    }
}
        </div>

        <div class="subsection-title">5.2 Event Management</div>
        <p>Event management includes CRUD operations with image handling:</p>

        <div class="code-block">
// Event Creation (admin/events.php)
if ($_SERVER['REQUEST_METHOD'] == 'POST' && $_POST['action'] == 'create') {
    $name = $_POST['name'];
    $description = $_POST['description'];
    $date = $_POST['date'];
    $time = $_POST['time'];
    $location = $_POST['location'];
    $price = $_POST['price'];
    $available_tickets = $_POST['available_tickets'];

    // Handle image upload
    $image = '';
    if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
        $target_dir = "../images/events/";
        $image = basename($_FILES["image"]["name"]);
        move_uploaded_file($_FILES["image"]["tmp_name"], $target_dir . $image);
    }

    // Insert event
    $stmt = $pdo->prepare("INSERT INTO events (name, description, date, time, location, price, available_tickets, image) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
    $stmt->execute([$name, $description, $date, $time, $location, $price, $available_tickets, $image]);
}
        </div>

        <div class="subsection-title">5.3 Shopping Cart System</div>
        <p>AJAX-powered shopping cart with real-time updates:</p>

        <div class="code-block">
// Add to Cart (ajax/add_to_cart.php)
session_start();
require_once '../config/database.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $event_id = $_POST['event_id'];
    $quantity = $_POST['quantity'];
    $user_id = $_SESSION['user_id'];

    // Check if item already in cart
    $stmt = $pdo->prepare("SELECT * FROM cart_items WHERE user_id = ? AND event_id = ?");
    $stmt->execute([$user_id, $event_id]);

    if ($stmt->rowCount() > 0) {
        // Update quantity
        $stmt = $pdo->prepare("UPDATE cart_items SET quantity = quantity + ? WHERE user_id = ? AND event_id = ?");
        $stmt->execute([$quantity, $user_id, $event_id]);
    } else {
        // Add new item
        $stmt = $pdo->prepare("INSERT INTO cart_items (user_id, event_id, quantity) VALUES (?, ?, ?)");
        $stmt->execute([$user_id, $event_id, $quantity]);
    }

    echo json_encode(['success' => true]);
}
        </div>

        <div class="subsection-title">5.4 QR Code Generation</div>
        <p>Digital ticket generation with QR codes:</p>

        <div class="code-block">
// QR Code Generation (includes/qr_generator.php)
function generateQRCode($booking_id, $event_name, $user_name) {
    $qr_data = "EventHive Ticket\n";
    $qr_data .= "Booking ID: " . $booking_id . "\n";
    $qr_data .= "Event: " . $event_name . "\n";
    $qr_data .= "Attendee: " . $user_name . "\n";
    $qr_data .= "Verify at: eventhive.cm/verify/" . $booking_id;

    // Generate QR code using library
    $qr_code_url = "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=" . urlencode($qr_data);

    return $qr_code_url;
}
        </div>

        <!-- 6. User Manual -->
        <div id="user-manual" class="section-title">6. User Manual</div>

        <div class="subsection-title">6.1 Getting Started</div>
        <div class="info-box">
            <h6><i class="fas fa-play-circle text-primary me-2"></i>Quick Start Guide</h6>
            <ol>
                <li>Visit the EventHive website</li>
                <li>Create an account by clicking "Register"</li>
                <li>Browse available events on the homepage</li>
                <li>Use search filters to find specific events</li>
                <li>Click on an event to view details</li>
                <li>Add tickets to your cart</li>
                <li>Proceed to checkout and complete payment</li>
                <li>Download your QR code tickets</li>
            </ol>
        </div>

        <div class="subsection-title">6.2 User Account Management</div>
        <p><strong>Registration Process:</strong></p>
        <ul>
            <li>Click "Register" in the top navigation</li>
            <li>Fill in username, email, and password</li>
            <li>Confirm your email address</li>
            <li>Complete your profile information</li>
        </ul>

        <p><strong>Profile Management:</strong></p>
        <ul>
            <li>Access your profile from the user menu</li>
            <li>Update personal information (name, email, phone)</li>
            <li>Change your password securely</li>
            <li>View account statistics and booking history</li>
            <li>Delete your account if needed (danger zone)</li>
        </ul>

        <div class="subsection-title">6.3 Event Discovery & Booking</div>
        <p><strong>Finding Events:</strong></p>
        <ul>
            <li>Browse featured events on the homepage</li>
            <li>Use the search bar to find specific events</li>
            <li>Filter by location (Yaoundé, Douala, etc.)</li>
            <li>Filter by date range</li>
            <li>View event categories (tech, music, business, etc.)</li>
        </ul>

        <p><strong>Booking Process:</strong></p>
        <ol>
            <li>Select an event and click "View Details"</li>
            <li>Review event information and pricing</li>
            <li>Choose number of tickets</li>
            <li>Add to cart or buy immediately</li>
            <li>Review cart contents</li>
            <li>Proceed to checkout</li>
            <li>Enter attendee information</li>
            <li>Complete payment process</li>
            <li>Receive confirmation email</li>
            <li>Download QR code tickets</li>
        </ol>

        <div class="subsection-title">6.4 Dashboard & Ticket Management</div>
        <p><strong>User Dashboard Features:</strong></p>
        <ul>
            <li>View upcoming and past events</li>
            <li>Download tickets with QR codes</li>
            <li>Print receipt copies</li>
            <li>Track booking status</li>
            <li>View spending statistics</li>
            <li>Filter bookings by date or status</li>
        </ul>

        <div class="warning-box">
            <h6><i class="fas fa-qrcode text-warning me-2"></i>QR Code Tickets</h6>
            <p>Your QR code tickets contain all necessary information for event entry. They work offline and can be scanned at the venue entrance. Always have a backup copy (printed or saved on your device).</p>
        </div>

        <div class="subsection-title">6.5 Administrator Guide</div>
        <p><strong>Admin Panel Access:</strong></p>
        <ul>
            <li>Navigate to /admin/login.php</li>
            <li>Enter admin credentials</li>
            <li>Access the admin dashboard</li>
        </ul>

        <p><strong>Event Management:</strong></p>
        <ul>
            <li>Create new events with details and images</li>
            <li>Edit existing event information</li>
            <li>Manage ticket availability</li>
            <li>Set event status (active/inactive)</li>
            <li>Monitor ticket sales</li>
        </ul>

        <p><strong>User Management:</strong></p>
        <ul>
            <li>View all registered users</li>
            <li>Monitor user activity</li>
            <li>Handle support requests</li>
            <li>Manage user accounts</li>
        </ul>

        <p><strong>Reports & Analytics:</strong></p>
        <ul>
            <li>View booking statistics</li>
            <li>Generate revenue reports</li>
            <li>Export data in CSV format</li>
            <li>Monitor platform performance</li>
        </ul>

        <!-- 7. Appendices -->
        <div id="appendices" class="section-title">7. Appendices</div>

        <div class="subsection-title">7.1 Troubleshooting</div>
        <div class="warning-box">
            <h6><i class="fas fa-exclamation-triangle text-warning me-2"></i>Common Issues</h6>
            <ul>
                <li><strong>Login Problems:</strong> Clear browser cache, check credentials</li>
                <li><strong>Payment Issues:</strong> Verify payment information, contact support</li>
                <li><strong>QR Code Problems:</strong> Ensure good lighting when scanning</li>
                <li><strong>Email Not Received:</strong> Check spam folder, verify email address</li>
                <li><strong>Site Loading Slowly:</strong> Check internet connection, try different browser</li>
            </ul>
        </div>

        <div class="subsection-title">7.2 Contact Information</div>
        <div class="info-box">
            <h6><i class="fas fa-envelope text-primary me-2"></i>Support Contacts</h6>
            <ul>
                <li><strong>Email:</strong> <EMAIL></li>
                <li><strong>Phone:</strong> +237 123 456 789</li>
                <li><strong>Location:</strong> Yaoundé, Cameroon</li>
                <li><strong>Response Time:</strong> Within 24 hours</li>
            </ul>
        </div>

        <div class="subsection-title">7.3 Legal Information</div>
        <p>EventHive operates under Cameroon law and includes comprehensive legal documentation:</p>
        <ul>
            <li><strong>Terms of Service:</strong> User agreement and platform rules</li>
            <li><strong>Privacy Policy:</strong> Data protection and user rights</li>
            <li><strong>Refund Policy:</strong> Event-specific cancellation terms</li>
            <li><strong>Cookie Policy:</strong> Website tracking and analytics</li>
        </ul>

        <!-- 8. References -->
        <div id="references" class="section-title">8. References</div>

        <div class="subsection-title">8.1 Technical References</div>
        <ul>
            <li>PHP Official Documentation: <a href="https://www.php.net/docs.php">https://www.php.net/docs.php</a></li>
            <li>MySQL Documentation: <a href="https://dev.mysql.com/doc/">https://dev.mysql.com/doc/</a></li>
            <li>Bootstrap Framework: <a href="https://getbootstrap.com/docs/">https://getbootstrap.com/docs/</a></li>
            <li>Font Awesome Icons: <a href="https://fontawesome.com/docs">https://fontawesome.com/docs</a></li>
            <li>QR Code API: <a href="https://goqr.me/api/">https://goqr.me/api/</a></li>
        </ul>

        <div class="subsection-title">8.2 Security References</div>
        <ul>
            <li>OWASP Security Guidelines: <a href="https://owasp.org/">https://owasp.org/</a></li>
            <li>PHP Security Best Practices: <a href="https://www.php.net/manual/en/security.php">PHP Security Manual</a></li>
            <li>Web Application Security: <a href="https://cheatsheetseries.owasp.org/">OWASP Cheat Sheets</a></li>
        </ul>

        <div class="subsection-title">8.3 Design References</div>
        <ul>
            <li>Web Accessibility Guidelines: <a href="https://www.w3.org/WAI/WCAG21/quickref/">WCAG 2.1</a></li>
            <li>Responsive Design Principles: <a href="https://web.dev/responsive-web-design-basics/">Web.dev</a></li>
            <li>User Experience Best Practices: <a href="https://www.nngroup.com/">Nielsen Norman Group</a></li>
        </ul>

        <hr class="my-5">

        <div class="text-center">
            <h5><i class="fas fa-calendar-alt text-primary me-2"></i>EventHive</h5>
            <p class="text-muted">Connecting Cameroon to Amazing Events</p>
            <div class="cameroon-flag"></div>
            <p class="mt-3"><small>© 2024 EventHive Cameroon. All rights reserved.</small></p>
        </div>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
